/*
 * @workOrder 工序作业平台-班组交接卡片
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024-10-14 13:52:47
 * @copyright Copyright (c) 2024 , Hand
 */
/* eslint-disable no-console */
import React, { useMemo, useEffect, useState, useRef } from 'react';
import { observer } from 'mobx-react';
import { round, isEmpty } from 'lodash';
import { Col, Row } from 'hzero-ui';
import {
  DataSet,
  Form,
  Button,
  NumberField,
  Output,
  Select,
  TextField,
  Modal,
} from 'choerodon-ui/pro';
// import notification from 'utils/notification';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { openTab } from 'utils/menuTab';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import { getCurrentOrganizationId } from 'utils/utils';
import { CardLayout, useRequest, ONotification } from '../commonComponents';
import { useAdhesiveFilmWorkbench, DispatchType } from '../../contextsStore';
import { FormDS } from './stores';
import { FetchHomeData, SaveRecordData, IotAutoQuery, JudgeAttribute } from './services';
import styles from './index.module.less';
import SwipingCard from './SwipingCard';

const TeamHandover = observer(props => {
  const { dispatch, enterInfo } = useAdhesiveFilmWorkbench();
  const { run: fetchHomeData, loading: fetchHomeDataLoading } = useRequest(FetchHomeData(), {
    manual: true,
    needPromise: true,
  });
  const { run: saveRecordData, loading: saveRecordDataLoading } = useRequest(SaveRecordData(), {
    manual: true,
    needPromise: true,
  });
  const { run: judgeAttribute } = useRequest(JudgeAttribute(), {
    manual: true,
    needPromise: true,
  });

  const { run: iotAutoQuery } = useRequest(IotAutoQuery(), {
    manual: true,
    needPromise: true,
  });

  const [orShiftUsefulOutput, setOrShiftUsefulOutput] = useState<any>(0);
  const [homeInfo, setHomeInfo] = useState<any>({});
  const [eoList, setEoList] = useState<any>([]);
  const teamLeaderRef = useRef(null);
  const swipingTimesRef = useRef(0);
  const [cardShow, setCardShow] = useState(false);
  // 是否监听输入框聚焦
  const [isInputFocus, setIsInputFocus] = useState(false);
  const tenantId = getCurrentOrganizationId();
  const [isSubmit, setIsSubmit] = useState(false);
  // 局部刷新，不用loading
  const [partFetchHomeDataLoading, setPartFetchHomeDataLoading] = useState(true);

  const formDs = useMemo(() => new DataSet(FormDS()), []);

  useEffect(() => {
    const { shiftCode, shiftDate, shiftTeamId, prodLineId } = enterInfo;
    if (enterInfo?.shiftTeamId) {
      const params = {
        shiftCode,
        shiftDate,
        shiftTeamId,
        prodLineId,
      };
      formDs.setState('enterInfo', enterInfo);
      queryFetchHomeData(params);
    }
  }, [enterInfo]);

  // 获取主页数据
  const queryFetchHomeData = async (params, updateEoListFlag = true, preserveFormData = false) => {
    // 缓存当前表单内容
    let cachedFormData = null;
    if (preserveFormData && formDs.current) {
      cachedFormData = formDs.current.toData();
    }

    const res = await fetchHomeData({ params });
    if (!res?.failed) {
      setHomeInfo(res);
      setOrShiftUsefulOutput(res?.shiftUsefulOutput || 0);

      // 如果缓存了表单数据，合并到新数据中
      if (cachedFormData) {
        const mergedData = Object.assign({}, res, cachedFormData);
        formDs.loadData([mergedData]);
      } else {
        formDs.loadData([res]);
      }

      if (updateEoListFlag) {
        setEoList(res?.eoList || []);
      } else if (eoList.length > 0) {
        eoList.forEach((item, index) => {
          handleLengthChange(item.length, index, res);
        });
      }
    }
  };

  const handleSubmit = async () => {
    // 判断eo是否输入
    // if (eoList.some(item => !item.length)) {
    //   return ONotification.warning({
    //     message: '交接内容中交接米数不能为空，请检查123！',
    //   });
    // }
    // 判断上报人
    const validate = await formDs.validate();
    if (!validate && !isEmpty(eoList)) {
      return ONotification.warning({
        message: '投料量、检验员等信息必输，请检查！',
      });
    }
    // 校验从刷卡后进行，避免刷卡前拦截

    // @ts-ignore
    teamLeaderRef.current = {};
    swipingTimesRef.current = 0;
    setIsInputFocus(true);
    setCardShow(true);
  };

  const handleSave = async () => {
    setIsSubmit(false);
    handleSaveData(isSubmit);
  };

  const handleUpdate = async flag => {
    setPartFetchHomeDataLoading(false);
    const { shiftCode, shiftDate, shiftTeamId, prodLineId } = enterInfo;
    if (enterInfo?.shiftTeamId) {
      const params = {
        shiftCode,
        shiftDate,
        shiftTeamId,
        prodLineId,
      };
      formDs.setState('enterInfo', enterInfo);
      // 点击刷新时候 eoList 不变
      await queryFetchHomeData(params, false, true);
      const res = await fetchHomeData({ params });
      if (!res?.failed) {
        const {
          assembleQty,
          powderBalance,
          balance,
          plasticizerAssembleQty,
          powderMaterialAssembleQty,
          sheetMaterialAssembleQty,
        } = res;
        formDs.current?.set('sheetMaterialAssembleQty', sheetMaterialAssembleQty);
        formDs.current?.set('powderMaterialAssembleQty', powderMaterialAssembleQty);
        formDs.current?.set('plasticizerAssembleQty', plasticizerAssembleQty);
        formDs.current?.set('balance', balance);
        formDs.current?.set('powderBalance', powderBalance);
        formDs.current?.set('assembleQty', assembleQty);
        if (flag) {
          await handleJudgeAttribute();
        }
      }
    }
    setTimeout(() => {
      setPartFetchHomeDataLoading(true);
    }, 500);
  };

  const fetchTeamMemberInfo = employeeNum => {
    request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-splicing-plan-sheets/team-member/query/ui`, {
      method: 'GET',
      query: {
        shiftDate: enterInfo?.shiftDate,
        shiftCode: enterInfo?.shiftCode,
        employeeNum,
      },
    }).then(res => {
      setIsInputFocus(true);
      if (res && res.success) {
        const params = res?.rows || {};
        params.isSecondedFlag = params.isSeconded || 'N';
        params.secondmentFlag = params.isSeconded || 'N';
        if (params.employeeType === 'TEAM_LEADER') {
          handleUpdate(true);
          ONotification.success({ message: '班组长扫描成功' });
          // 刷卡成功后关闭刷卡界面
          setCardShow(false);
          setIsInputFocus(false);
        } else {
          ONotification.error({ message: '班组长信息未扫描，请检查！' });
        }
      } else {
        ONotification.error({ message: res.message });
      }
    });
  };

  const handleJudgeAttribute = async () => {
    const { shiftTeamId, shiftDate, shiftCode, prodLineId } = formDs?.getState('enterInfo') || {};
    const res = await judgeAttribute({
      params: {
        ...homeInfo,
        ...formDs.toData()[0],
        makeRate: homeInfo?.makeRate,
        productRate: homeInfo?.productRate,
        shiftUsefulOutput: Number(homeInfo?.shiftUsefulOutput),
        shiftTeamId,
        shiftDate,
        shiftCode,
        prodLineId,
        eoList,
        ...(isSubmit && { operationType: 'S' }),
      },
    });
    if (res.attribute15 !== 'Y') {
      ONotification.error({
        message: '当班尚未完成投料，请联系发料员！',
      });
      setCardShow(false);
      setIsInputFocus(false);
    } else {
      // 刷卡成功后校验用量输入值非0
      const { sheetMaterialUsed, powderMaterialUsed, plasticizer } = formDs.current?.toData() || {};
      if (!sheetMaterialUsed || !powderMaterialUsed || !plasticizer) {
        ONotification.warning({
          message: '片料用量、粉料用量、增塑剂量输入值不能为0，请检查！',
        });
        // 用量输入值为0时关闭刷卡界面
        setCardShow(false);
        setIsInputFocus(false);
        return;
      }

      if (Number(homeInfo?.makeRate) > 99) {
        Modal.confirm({
          title: '提示',
          children: (
            <div style={{ color: '#fff', fontSize: 14 }}>当前产线出品率超99%，请确认！</div>
          ),
          onOk: async () => {
            await handleSaveData(isSubmit);
          },
        });
      } else {
        await handleSaveData(isSubmit);
      }
    }
  };

  const onInputKeydownNum = val => {
    setIsInputFocus(false);
    fetchTeamMemberInfo(val);
  };

  const swipingCardProps = {
    cardShow,
    isInputFocus,
    onInputKeydownNum,
    closeCarShow: () => {
      setCardShow(false);
      setIsInputFocus(false);
    },
  };

  // 交接上报
  const handleSaveData = async (isSubmit = false) => {
    const { shiftTeamId, shiftDate, shiftCode, prodLineId } = formDs?.getState('enterInfo') || {};

    const latestHomeData = await fetchHomeData({
      params: {
        shiftCode,
        shiftDate,
        shiftTeamId,
        prodLineId,
      },
    });

    const formData = (formDs.toData()[0] || {}) as any;

    const saveData = {
      ...latestHomeData,
      sheetMaterialUsed: formData?.sheetMaterialUsed,
      powderMaterialUsed: formData?.powderMaterialUsed,
      plasticizer: formData?.plasticizer,
      colorBarUsed: formData?.colorBarUsed,
      smallAdditives: formData?.smallAdditives,
      isolationFilmScrapQty: formData?.isolationFilmScrapQty,
      selfInspectByOne: formData?.selfInspectByOne,
      selfInspectByTwo: formData?.selfInspectByTwo,
      shiftReleaseBy: formData?.shiftReleaseBy,
      shiftInspectBy: formData?.shiftInspectBy,
      isolationMembraneSelfCheck: formData?.isolationMembraneSelfCheck,
      shiftUsefulOutput: Number(latestHomeData?.shiftUsefulOutput),
      shiftTeamId,
      shiftDate,
      shiftCode,
      prodLineId,
      eoList,
      ...(isSubmit && { operationType: 'S' }),
    };

    console.log(saveData, 'saveData');
    

    // const res = await saveRecordData({
    //   params: saveData,
    // });
    // if (res?.failed) {
    //   ONotification.error({
    //     message: res?.message,
    //   });
    //   await queryFetchHomeData(
    //     {
    //       shiftCode,
    //       shiftDate,
    //       shiftTeamId,
    //       prodLineId,
    //     },
    //     true,
    //     true,
    //   );
    //   return false;
    // }
    // ONotification.success({
    //   message: isSubmit ? '交接上报成功！' : '保存成功！',
    // });
    // setCardShow(false);
    // setIsInputFocus(false);
    // if (isSubmit) {
    //   dispatch({
    //     type: DispatchType.update,
    //     payload: {
    //       logined: false,
    //     },
    //   });
    //   openTab({
    //     title: '胶膜工作台',
    //     key: '/hmes/adhesive-film-workbench',
    //     path: '/hmes/adhesive-film-workbench',
    //     closable: true,
    //   });
    // }
  };
  // 重新渲染增加后缀
  const rendererSuffix = (value, suffix) => {
    if (value === null || value === undefined) {
      return '';
    }
    return `${value}  ${suffix}`;
  };

  // 交接米数修改
  const handleLengthChange = (e, i, obj = false) => {
    const item = eoList[i];
    // 验证交接米数不能大于工单的生产米数
    if (e > Number(item.metersPerRoll)) {
      ONotification.error({
        message: '交接米数不得大于生产米数',
      });
      return;
    }
    const weight = round(e * item.materialWidth * 1.07 * Number(item.prodTargetValue) * 1000, 2);
    const newList = eoList.map((item, index) =>
      index === i ? { ...item, length: e, weight } : item,
    );
    setEoList(newList);
    const weightSum = newList.reduce((acc, item) => acc + item.weight, 0);
    const homeInfoNew = obj || homeInfo;
    // 重新计算成品率和出品率
    if (homeInfoNew.assembleQty && homeInfoNew.assembleQty !== 0) {
      const totalNumber = Number(
        Number(orShiftUsefulOutput || 0) +
          Number(homeInfoNew.returnMaterialWeight || 0) +
          Number(weightSum || 0),
      );
      const newProductRate =
        (Number(orShiftUsefulOutput || 0) + Number(weightSum || 0)) /
        Number(
          Number(homeInfoNew.assembleQty) -
            Number(homeInfoNew.balance) -
            Number(homeInfoNew.powderBalance) +
            Number(homeInfoNew.previousBalance) +
            Number(homeInfoNew.previousPowderBalance),
        );
      const newMakeRate =
        Number(totalNumber) /
        Number(
          Number(homeInfoNew.assembleQty) -
            Number(homeInfoNew.balance) -
            Number(homeInfoNew.powderBalance) +
            Number(homeInfoNew.previousBalance) +
            Number(homeInfoNew.previousPowderBalance),
        );
      setHomeInfo({
        ...homeInfoNew,
        makeRate: Number(Number(newMakeRate * 100)?.toFixed(2)),
        productRate: Number(Number(newProductRate * 100)?.toFixed(2)),
        shiftUsefulOutput: (Number(orShiftUsefulOutput) + (weightSum || 0))?.toFixed(2),
      });
      const currentFormData = formDs.current?.toData() || {};
      formDs.loadData([
        {
          ...homeInfoNew,
          makeRate: Number(Number(newMakeRate * 100)?.toFixed(2)),
          productRate: Number(Number(newProductRate * 100)?.toFixed(2)),
          shiftUsefulOutput: (Number(orShiftUsefulOutput) + (weightSum || 0))?.toFixed(2),
          sheetMaterialUsed: currentFormData.sheetMaterialUsed ?? homeInfoNew.sheetMaterialUsed,
          powderMaterialUsed: currentFormData.powderMaterialUsed ?? homeInfoNew.powderMaterialUsed,
          plasticizer: currentFormData.plasticizer ?? homeInfoNew.plasticizer,
          colorBarUsed: currentFormData.colorBarUsed ?? homeInfoNew.colorBarUsed,
          isolationFilmScrapQty:
            currentFormData.isolationFilmScrapQty ?? homeInfoNew.isolationFilmScrapQty,
          sheetPowderMaterialTotal:
            currentFormData.sheetPowderMaterialTotal ?? homeInfoNew.sheetPowderMaterialTotal,
        },
      ]);
    } else {
      setHomeInfo({
        ...homeInfoNew,
        shiftUsefulOutput: (Number(orShiftUsefulOutput) + (weightSum || 0))?.toFixed(2),
      });
      const currentFormData = formDs.current?.toData() || {};
      formDs.loadData([
        {
          ...homeInfoNew,
          shiftUsefulOutput: (Number(orShiftUsefulOutput) + (weightSum || 0))?.toFixed(2),
          sheetMaterialUsed: currentFormData.sheetMaterialUsed ?? homeInfoNew.sheetMaterialUsed,
          powderMaterialUsed: currentFormData.powderMaterialUsed ?? homeInfoNew.powderMaterialUsed,
          plasticizer: currentFormData.plasticizer ?? homeInfoNew.plasticizer,
          colorBarUsed: currentFormData.colorBarUsed ?? homeInfoNew.colorBarUsed,
          isolationFilmScrapQty:
            currentFormData.isolationFilmScrapQty ?? homeInfoNew.isolationFilmScrapQty,
          sheetPowderMaterialTotal:
            currentFormData.sheetPowderMaterialTotal ?? homeInfoNew.sheetPowderMaterialTotal,
        },
      ]);
    }
  };

  const tagMappings = [
    { code: 'PL', key: 'sheetMaterialUsed' },
    { code: 'SZF', key: 'powderMaterialUsed' },
    { code: 'ZSJ', key: 'plasticizer' },
    { code: 'PCJ', key: 'colorBarUsed' },
  ];

  const getIotData = async () => {
    const res = await iotAutoQuery({
      params: {
        workcellId: enterInfo?.workcellDetailList.length
          ? enterInfo.workcellDetailList[0].workcellId
          : '',
        tagCodeList: ['PL', 'SZF', 'ZSJ', 'PCJ'],
        shiftHandoverFlag: 'Y',
      },
    });
    if (res?.success) {
      if (res.rows?.length) {
        const total = tagMappings.reduce((acc, { code, key }) => {
          const item = res.rows.find(item => item.tagCode === code);
          const value = item ? item.tagValue : 0;
          formDs.current?.set(key, value);
          return acc + value;
        }, 0);
        formDs.current?.set('sheetPowderMaterialTotal', total);
      } else {
        ONotification.error({ message: '未获取到设备采集信息，请检查！' });
        return false;
      }
    } else {
      ONotification.error({ message: res?.message });
      return false;
    }
  };

  return (
    <CardLayout.Layout
      spinning={(fetchHomeDataLoading || saveRecordDataLoading) && partFetchHomeDataLoading}
      className={styles.teamHandover}
    >
      <CardLayout.Header
        className="SelfMutualInspectionHead"
        title="班组交接"
        help={props?.cardUsage?.remark}
      />
      <CardLayout.Content className="SelfMutualInspectionForm">
        <Row>
          <Col span={19}>
            <h3>班次作业一览表</h3>
            {!isEmpty(homeInfo) && (
              <>
                <Form dataSet={formDs} columns={4} labelWidth={150}>
                  <Output
                    name="totalOutput"
                    renderer={({ value }) => rendererSuffix(value, '卷')}
                  />
                  <Output
                    name="outputWeight"
                    renderer={({ value }) => rendererSuffix(value, 'KG')}
                  />
                  <Output
                    name="previousVolume"
                    renderer={({ value }) => rendererSuffix(value, '卷')}
                  />
                  <Output
                    name="previousWeight"
                    renderer={({ value }) => rendererSuffix(value, 'KG')}
                  />
                  <Output
                    name="downgradeVolume"
                    renderer={({ value }) => rendererSuffix(value, '卷')}
                  />
                  <Output
                    name="downgradeWeight"
                    renderer={({ value }) => rendererSuffix(value, 'KG')}
                  />
                  <Output
                    name="scrapVolume"
                    renderer={({ value }) => rendererSuffix(value, '卷')}
                  />
                  <Output
                    name="scrapWeight"
                    renderer={({ value }) => rendererSuffix(value, 'KG')}
                  />
                  <Output
                    name="separationFilmUsed"
                    renderer={({ value }) => rendererSuffix(value, 'KG')}
                  />
                  <Output
                    name="separationFilmWeight"
                    renderer={({ value }) => rendererSuffix(value, 'KG')}
                  />
                  <Output
                    name="tubeCoreUsed"
                    renderer={({ value }) => rendererSuffix(value, 'KG')}
                  />
                  <Output
                    name="receiveUsed"
                    renderer={({ value }) => rendererSuffix(value, 'KG')}
                  />
                  <Output name="ncFrequency" renderer={({ value }) => rendererSuffix(value, '')} />
                  <Output name="ncTime" renderer={({ value }) => rendererSuffix(value, '分')} />
                  <Output
                    name="returnMaterialWeight"
                    renderer={({ value }) => rendererSuffix(value, 'KG')}
                  />
                  <Output
                    name="attendancePersonSum"
                    renderer={({ value }) => rendererSuffix(value, '人')}
                  />
                  <TextField name="isolationFilmScrapQty" />
                </Form>
                <Form dataSet={formDs} columns={2} labelWidth={150}>
                  <h3>失重称投料量</h3>
                  <h3>扫码投料量</h3>
                  <TextField name="sheetMaterialUsed" />
                  <TextField name="sheetMaterialAssembleQty" disabled />
                  <TextField name="powderMaterialUsed" />
                  <TextField name="powderMaterialAssembleQty" disabled />
                  <TextField name="plasticizer" />
                  <TextField name="plasticizerAssembleQty" disabled />
                  <TextField name="colorBarUsed" />
                  <TextField name="balance" disabled />
                  <TextField name="sheetPowderMaterialTotal" disabled />
                  <TextField name="powderBalance" disabled />
                  <Button onClick={() => getIotData()}>获取</Button>
                  <TextField name="assembleQty" disabled />
                </Form>
              </>
            )}
            <h3>交接内容</h3>
            <div className={styles.connectList}>
              <div style={{ display: 'flex' }}>
                {eoList.map((item, index) => (
                  <div className={styles.scrollBox}>
                    <div>
                      <span>拼卷{index + 1}: </span>
                      <span>{item.materialName}</span>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <span>交接米数: </span>
                      <NumberField
                        required
                        value={item.length}
                        onChange={e => handleLengthChange(e, index, false)}
                        style={{ width: '80px' }}
                      />
                    </div>
                    <div>
                      <span>交接重量: </span>
                      <span>{item.weight}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <h3>检验员信息</h3>
            {!isEmpty(homeInfo) && !isEmpty(eoList) && (
              <Form dataSet={formDs} columns={4} labelWidth={150}>
                <Select name="selfInspectByOne" />
                <Select name="selfInspectByTwo" />
                <Select name="shiftReleaseBy" />
                <Select name="shiftInspectBy" />
                <Select name="isolationMembraneSelfCheck" />
              </Form>
            )}
          </Col>
          <Col span={5}>
            {!isEmpty(homeInfo) && (
              <div className={styles.leftInfoContainer}>
                <div className={styles.statsWrapper}>
                  <div className={styles.statItem}>
                    <span className={styles.statLabel}>产量统计：</span>
                    <span className={styles.statValue}>{homeInfo.totalOutput} 卷</span>
                  </div>
                  <div className={styles.statItem}>
                    <span className={styles.statLabel}>本班有效产量：</span>
                    <span className={styles.statValue}>{homeInfo.shiftUsefulOutput}</span>
                  </div>
                  <div className={styles.statItem}>
                    <span className={styles.statLabel}>本班产出总量：</span>
                    <span className={styles.statValue}>
                      {(
                        Number(homeInfo.shiftUsefulOutput) +
                        Number(homeInfo?.returnMaterialWeight || 0)
                      ).toFixed(2)}
                    </span>
                  </div>
                  <div className={styles.statItem}>
                    <span className={styles.statLabel}>本班投料总量：</span>
                    <span className={styles.statValue}>
                      {Number(homeInfo?.totalQty)?.toFixed(2)}
                    </span>
                  </div>
                  <div className={styles.statItem}>
                    <span className={styles.statLabel}>出品率：</span>
                    <span className={styles.statValue}>{`${homeInfo?.makeRate}%`}</span>
                  </div>
                  <div className={styles.statItem}>
                    <span className={styles.statLabel}>成品率：</span>
                    <span className={styles.statValue}>{`${homeInfo?.productRate}%`}</span>
                  </div>
                </div>
              </div>
            )}
          </Col>
        </Row>
      </CardLayout.Content>
      <div className={styles.cardFooter}>
        <div style={{ color: '#ccff00', fontSize: '24px', fontWeight: 600 }}>
          为保证成品/出品率准确，请刷新后填写交接数据！
        </div>
        <div className={styles.printButton}>
          <Button
            color={ButtonColor.primary}
            className={styles.buttonOk}
            onClick={() => handleUpdate(false)}
          >
            刷新
          </Button>
          <Button
            color={ButtonColor.primary}
            className={styles.buttonOk}
            disabled={!!homeInfo?.shiftHandoverLogId || isEmpty(homeInfo)}
            onClick={handleSave}
          >
            保存
          </Button>
          <Button
            color={ButtonColor.primary}
            className={styles.buttonOk}
            disabled={!!homeInfo?.shiftHandoverLogId || isEmpty(homeInfo)}
            onClick={() => {
              setIsSubmit(true);
              handleSubmit();
            }}
          >
            交接上报
          </Button>
        </div>
      </div>
      <SwipingCard {...swipingCardProps} />
    </CardLayout.Layout>
  );
});

export default TeamHandover;
